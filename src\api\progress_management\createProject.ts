import request from "@/utils/request";
import {
  Project,
  ProjectForm,
  User,
  TaskRow,
  TaskForm,
  UploadFileParams,
  UploadedFile,
  ProjectStat,
  Milestone,
  StatusChartItem,
  DivisionChartItem,
} from "@/types/project";

// 审批文件接口
export interface ApprovalFile {
  fileId: string;
  name: string;
  url: string;
}

// 项目概览数据接口 - 新增近四年任务完成数量字段
export interface ProjectOverviewData {
  stats: ProjectStat[];
  milestones: Milestone[];
  statusChartData: StatusChartItem[];
  divisionChartData: DivisionChartItem[];
  yearlyTaskCounts: { year: number; taskCount: number }[];
  // 新增：近四年任务完成数量列表，按年份倒序排列（最近一年在前）
  recentFourYearsCompletedTasks: {
    year: number; // 年份
    completedCount: number; // 完成的任务数量
    totalCount: number; // 总任务数量
    completionRate: number; // 完成率（百分比，如85表示85%）
  }[];
  metadata: {
    yearList: number[];
    projectList: Project[];
  };
}

export interface ReportData {
  year: number;
  month: number;
  total: number;
  completed: number;
}

const BASE_URL = "/api/projects";
const USER_URL = "/api/users";
const TASK_URL = "/api/tasks";

class TaskAPI {
  /**
   * 获取任务列表
   * @param projectId 项目ID
   * @returns 任务列表
   */
  static getList(projectId: number) {
    return request<TaskRow[]>({
      url: `${TASK_URL}/${projectId}`,
      method: "get",
    });
  }

  /**
   * 创建任务
   * @param projectId 项目ID
   * @param data 任务表单数据
   * @returns 创建结果
   */
  static create(projectId: number, data: TaskForm) {
    return request({
      url: `${TASK_URL}/${projectId}`,
      method: "post",
      data,
    });
  }

  /**
   * 更新任务
   * @param projectId 项目ID
   * @param id 任务ID
   * @param data 任务表单数据
   * @returns 更新结果
   */
  static update(projectId: number, id: number, data: TaskForm) {
    return request({
      url: `${TASK_URL}/${projectId}/${id}`,
      method: "put",
      data,
    });
  }

  /**
   * 删除任务
   * @param projectId 项目ID
   * @param id 任务ID
   * @returns 删除结果
   */
  static delete(projectId: number, id: number) {
    return request({
      url: `${TASK_URL}/${projectId}/${id}`,
      method: "delete",
    });
  }

  /**
   * 上传任务相关文件
   * @param params 上传参数，包含任务ID和文件列表
   * @returns 上传成功的文件信息
   */
  static uploadFiles(params: UploadFileParams) {
    const { taskId, files } = params;
    const formData = new FormData();
    files.forEach((file) => {
      formData.append("files", file);
    });
    formData.append("taskId", taskId.toString());

    return request<UploadedFile[]>({
      url: "/api/upload-files",
      method: "post",
      data: formData,
      headers: { "Content-Type": "multipart/form-data" },
    });
  }

  /**
   * 获取任务的审批文件
   * @param taskId 任务ID
   * @returns 审批文件列表
   */
  static getApprovalFiles(taskId: number) {
    return request<ApprovalFile[]>({
      url: `${TASK_URL}/approval-files/${taskId}`,
      method: "get",
    });
  }

  /**
   * 更新任务审批状态为通过
   * @param taskId 任务ID
   * @returns 无返回值
   */
  static updateApprovalStatus(taskId: number) {
    return request({
      url: `${TASK_URL}/approval-status/${taskId}`,
      method: "get",
    });
  }

  /**
   * 更新任务审批状态为驳回
   * @param taskId 任务ID
   * @returns 无返回值
   */
  static rejectApproval(taskId: number,suggestion?:string) {
    return request({
      url: `${TASK_URL}/reject-approval/${taskId}/${suggestion}`,
      method: "post",
    });
  }

  /**
   * 下载任务相关文件
   * @param fileId 文件ID
   * @returns 文件的 Blob 数据
   */
  static downloadFile(fileId: string) {
    return request<Blob>({
      url: `/api/files/${fileId}`,
      method: "get",
      responseType: "blob",
    });
  }
  
  /**
   * 更改文件夹、文件位置
   * @param projectId 目标移动文件夹ID
   * @param taskId 待移动文件夹任务ID
   * @returns 无返回值
   */
  static switchFolder(queryParams: { newId: number; taskId: number }){
    return request({
      url: `${TASK_URL}/switch-folder`,
      method: "get",
      params:queryParams
    });
  }
}

class ProjectAPI {
  /** 获取年份列表 */
  static getYears() {
    return request<number[]>({
      url: `${BASE_URL}/report/getYears`,
      method: "get",
    });
  }

  /**
   * 获取项目列表
   * @returns 项目列表
   */
  static getList(queryParams: { pageNum?: number; pageSize?: number }) {
    return request<{
      total: number;
      page?: number;
      pageSize?: number;
      totalPages: number;
      items: Project[];
    }>({
      url: BASE_URL,
      method: "get",
      params: queryParams,
    });
  }

  /**
   * 获取根据年季度条件的项目列表
   * @param queryParams 查询参数，包含年和季度
   * @returns 项目分页列表
   */
  static getListByCondition(queryParams: {
    year: number;
    quarter: number;
    pageNum: number;
    pageSize: number;
  }) {
    return request<{
      total: number;
      page: number;
      pageSize: number;
      totalPages: number;
      items: Project[];
    }>({
      url: `${BASE_URL}/report/list`,
      method: "get",
      params: queryParams,
    });
  }

  /**
   * 获取项目详情
   * @param id 项目ID
   * @returns 项目详情
   */
  static getDetail(id: number) {
    return request<Project>({
      url: `${BASE_URL}/${id}`,
      method: "get",
    });
  }

  /**
   * 获取项目概览综合数据
   * @param queryParams 查询年月
   * @returns 项目概览数据，包含近四年project完成数量
   */
  static getReportOverview(queryParams: { year: number; quarter?: number }) {
    return request<ReportData[]>({
      url: `${BASE_URL}/report`,
      params: queryParams,
      method: "get",
    });
  }

  /**
   * 获取项目概览综合数据
   * @returns 项目概览数据，包含近四年project完成数量
   */
  static getProjectOverview() {
    return request<ProjectOverviewData>({
      url: `${BASE_URL}/overview`,
      method: "get",
    });
  }

  /**
   * 获取项目概览数据
   * @param id 项目ID（0表示所有项目）
   * @returns 项目概览数据，包含近四年任务完成数量
   */
  static getOverview(id: number) {
    return request<ProjectOverviewData>({
      url: `${TASK_URL}/statistics/overviewFourYear`,
      method: "get",
    });
  }

  /**
   * 创建项目
   * @param data 项目表单数据
   * @returns 创建结果
   */
  static create(data: ProjectForm) {
    return request({
      url: BASE_URL,
      method: "post",
      data,
    });
  }

  /**
   * 更新项目
   * @param id 项目ID
   * @param data 项目表单数据
   * @returns 更新结果
   */
  static update(id: number, data: ProjectForm) {
    return request({
      url: `${BASE_URL}/${id}`,
      method: "put",
      data,
    });
  }

  /**
   * 删除项目
   * @param id 项目ID
   * @returns 删除结果
   */
  static delete(id: number) {
    return request({
      url: `${BASE_URL}/${id}`,
      method: "delete",
    });
  }
}

class UserAPI {
  /**
   * 获取用户列表
   * @returns 用户列表
   */
  static getList() {
    return request<User[]>({
      url: USER_URL,
      method: "get",
    });
  }
}

export { TaskAPI, ProjectAPI, UserAPI };
